import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:imed_fe/core/constants/app_constants.dart';
import 'package:imed_fe/core/widgets/custom_elevated_button.dart';
import 'package:imed_fe/core/widgets/custom_outlined_button.dart';

import '../../domain/entities/doctor_availability_schedule.dart';
import '../bloc/availability_bloc.dart';
import '../bloc/availability_event.dart';
import '../bloc/availability_state.dart';
import '../widgets/day_availability_card.dart' show DayAvailabilityCard;
import '../widgets/date_picker_field.dart';

class AvailabilityPage extends StatefulWidget {
  const AvailabilityPage({super.key});

  @override
  State<AvailabilityPage> createState() => _AvailabilityPageState();
}

class _AvailabilityPageState extends State<AvailabilityPage> {
  DateTime selectedDate = DateTime.now();
  final String doctorId = '42b53451-a554-4c24-b2ae-bf025b4a966a';

  // Day availability state
  final Map<String, bool> _dayAvailability = {
    'Sunday': true,
    'Monday': true,
    'Tuesday': false,
    'Wednesday': false,
    'Thursday': true,
    'Friday': true,
    'Saturday': true,
  };

  // Time slots for each day
  final Map<String, List<TimeSlotData>> _dayTimeSlots = {
    'Sunday': [
      TimeSlotData(id: 'sunday_1', startTime: '09:00 AM', endTime: '09:00 PM'),
    ],
    'Monday': [
      TimeSlotData(id: 'monday_1', startTime: '09:00 AM', endTime: '09:00 PM'),
    ],
    'Tuesday': [],
    'Wednesday': [],
    'Thursday': [
      TimeSlotData(
        id: 'thursday_1',
        startTime: '09:00 AM',
        endTime: '09:00 PM',
      ),
    ],
    'Friday': [
      TimeSlotData(id: 'friday_1', startTime: '09:00 AM', endTime: '09:00 PM'),
    ],
    'Saturday': [
      TimeSlotData(
        id: 'saturday_1',
        startTime: '09:00 AM',
        endTime: '09:00 PM',
      ),
    ],
  };

  bool _repeatAvailability = false;
  late DateTime _startDate;
  late DateTime _endDate;

  // Unavailable dates state
  final List<UnavailableDate> _unavailableDates = [];

  @override
  void initState() {
    super.initState();

    // Initialize dates
    _startDate = DateTime.now();
    _endDate = DateTime.now().add(const Duration(days: 30));

    _loadAvailability();
  }

  void _loadAvailability() {
    context.read<AvailabilityBloc>().add(
      LoadDoctorAvailability(doctorId: doctorId, date: selectedDate),
    );
  }

  void _saveAvailability() {
    try {
      // Use the stored unavailable dates
      final unavailableDates = List<UnavailableDate>.from(_unavailableDates);

      // Convert day availability and time slots
      final daySchedules = <DaySchedule>[];
      final daysOfWeek = [
        'sunday',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
      ];

      for (final day in daysOfWeek) {
        final isAvailable = _dayAvailability[day] ?? false;
        final timeSlots = _dayTimeSlots[day] ?? [];

        final slots =
            timeSlots.map((slot) {
              return AvailabilityTimeSlot(
                startTime: AvailabilityTimeSlot.convertTo24HourFormat(
                  slot.startTime,
                ),
                endTime: AvailabilityTimeSlot.convertTo24HourFormat(
                  slot.endTime,
                ),
              );
            }).toList();

        daySchedules.add(
          DaySchedule(
            day: day.toLowerCase(),
            isOffDay: !isAvailable,
            slots: slots,
          ),
        );
      }

      // Create the schedule entity
      final schedule = DoctorAvailabilitySchedule(
        startDate: _startDate,
        endDate: _endDate,
        isRepeat: _repeatAvailability,
        unavailableDates: unavailableDates,
        days: daySchedules,
      );

      // Dispatch the event
      context.read<AvailabilityBloc>().add(
        SaveDoctorAvailabilitySchedule(schedule: schedule),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error preparing data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Shows dialog for marking unavailable dates
  void _showMarkUnavailableDatesDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _MarkUnavailableDatesDialog(
          existingDates: _unavailableDates,
          onSave: (List<UnavailableDate> dates) {
            setState(() {
              _unavailableDates.clear();
              _unavailableDates.addAll(dates);
            });
          },
        );
      },
    );
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && mounted) {
      setState(() {
        _startDate = picked;
        // Ensure end date is not before start date
        if (_endDate.isBefore(_startDate)) {
          _endDate = _startDate.add(const Duration(days: 30));
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && mounted) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AvailabilityBloc, AvailabilityState>(
      listener: (context, state) {
        if (state is AvailabilitySaving) {
          // Show loading indicator
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 16),
                  Text('Saving availability...'),
                ],
              ),
              duration: Duration(seconds: 30),
            ),
          );
        } else if (state is AvailabilitySaved) {
          // Hide loading and show success
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Availability saved successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (state is AvailabilityError) {
          // Hide loading and show error
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${state.message}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppConstants.backgroundColor,
        body: BlocBuilder<AvailabilityBloc, AvailabilityState>(
          builder: (context, state) {
            if (state is AvailabilityLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading availability data...'),
                  ],
                ),
              );
            } else if (state is AvailabilityError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading availability data',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadAvailability,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            // Default content (AvailabilityInitial, AvailabilityLoaded, etc.)
            return Padding(
              padding: const EdgeInsets.only(
                top: 32,
                left: 50,
                right: 200,
                bottom: 24,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildTopPanel(),
                  const SizedBox(width: 24),

                  Expanded(child: _buildRightPanel()),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Text(
      'My Availability',
      style: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: AppConstants.textPrimaryColor,
      ),
    );
  }

  Widget _buildTopPanel() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(
          color: AppConstants.textMidColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Set Availability',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Start Date
              DatePickerField(
                label: 'Start Date',
                selectedDate: _startDate,
                onTap: _selectStartDate,
              ),
              const SizedBox(width: 16),
              DatePickerField(
                label: 'Till',
                selectedDate: _endDate,
                onTap: _selectEndDate,
              ),
              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Once the slots are filled up the calendar will automatically open for another 2 weeks',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: Colors.black.withValues(alpha: 0.5),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Switch(
                          value: _repeatAvailability,
                          onChanged: (value) {
                            setState(() {
                              _repeatAvailability = value;
                            });
                          },
                          activeTrackColor: AppConstants.primaryColor,
                          activeThumbColor: Colors.white,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Repeat Availability (Not changed)',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: AppConstants.textHighColor,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Mark Unavailable Dates section
          CustomOutlinedButton(
            text: 'Mark Unavailable Dates',

            foregroundColor: AppConstants.primaryColor,
            borderColor: AppConstants.primaryColor,
            borderRadius: AppConstants.borderRadiusSmall,
            prefixIcon: Icon(
              Icons.calendar_today_outlined,
              color: AppConstants.primaryColor,
            ),
            width: double.infinity,
            onPressed: _showMarkUnavailableDatesDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildRightPanel() {
    return Column(
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Day cards
        Expanded(
          child: ListView(
            children:
                _dayAvailability.entries.map((entry) {
                  return DayAvailabilityCard(
                    dayName: entry.key,
                    isActive: entry.value,
                    timeSlots: _dayTimeSlots[entry.key] ?? [],
                    onToggle: (value) {
                      setState(() {
                        _dayAvailability[entry.key] = value;
                      });
                    },
                    onAddSlot: () {
                      setState(() {
                        _dayTimeSlots[entry.key]?.add(
                          TimeSlotData(
                            id: 'slot_${DateTime.now().millisecondsSinceEpoch}',
                            startTime: '',
                            endTime: '',
                          ),
                        );
                      });
                    },
                    onTimeSelect: _selectSlotTime,
                    onDeleteSlot: _deleteTimeSlot,
                  );
                }).toList(),
          ),
        ),

        const SizedBox(height: 24),
        SizedBox(
          width: 300,
          child: CustomElevatedButton(
            text: 'Save Availability',
            onPressed: _saveAvailability,
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Future<void> _selectSlotTime(TimeSlotData slot, bool isStartTime) async {
    // Parse current time if available
    TimeOfDay initialTime = TimeOfDay.now();
    if (isStartTime) {
      initialTime = _parseTimeString(slot.startTime);
    } else {
      initialTime = _parseTimeString(slot.endTime);
    }

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (picked != null && mounted) {
      final formattedTime = picked.format(context);
      setState(() {
        // Find which day this slot belongs to and replace it
        for (final dayName in _dayTimeSlots.keys) {
          final slots = _dayTimeSlots[dayName];
          if (slots != null) {
            final index = slots.indexOf(slot);
            if (index != -1) {
              // Replace the slot with a new one with updated time
              slots[index] = TimeSlotData(
                id: slot.id,
                startTime: isStartTime ? formattedTime : slot.startTime,
                endTime: isStartTime ? slot.endTime : formattedTime,
              );
              break;
            }
          }
        }
      });
    }
  }

  void _deleteTimeSlot(TimeSlotData slot) {
    setState(() {
      // Find which day this slot belongs to and remove it
      for (final dayName in _dayTimeSlots.keys) {
        final slots = _dayTimeSlots[dayName];
        if (slots != null && slots.contains(slot)) {
          slots.remove(slot);
          break;
        }
      }
    });
  }

  TimeOfDay _parseTimeString(String timeString) {
    try {
      // Parse time string in format "h:mm AM/PM"
      final parts = timeString.split(' ');
      final timePart = parts[0];
      final period = parts.length > 1 ? parts[1] : 'AM';

      final timeParts = timePart.split(':');
      int hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      if (period.toUpperCase() == 'PM' && hour != 12) {
        hour += 12;
      } else if (period.toUpperCase() == 'AM' && hour == 12) {
        hour = 0;
      }

      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      return TimeOfDay.now();
    }
  }
}

/// Dialog for marking unavailable dates
class _MarkUnavailableDatesDialog extends StatefulWidget {
  final List<UnavailableDate> existingDates;
  final Function(List<UnavailableDate>) onSave;

  const _MarkUnavailableDatesDialog({
    required this.existingDates,
    required this.onSave,
  });

  @override
  State<_MarkUnavailableDatesDialog> createState() =>
      _MarkUnavailableDatesDialogState();
}

class _MarkUnavailableDatesDialogState
    extends State<_MarkUnavailableDatesDialog> {
  late List<_UnavailableDateEntry> _dateEntries;

  @override
  void initState() {
    super.initState();
    // Initialize with existing dates or add one empty entry
    _dateEntries =
        widget.existingDates
            .map((date) => _UnavailableDateEntry.fromEntity(date))
            .toList();
    if (_dateEntries.isEmpty) {
      _dateEntries.add(_UnavailableDateEntry());
    }
  }

  void _addNewDateEntry() {
    setState(() {
      _dateEntries.add(_UnavailableDateEntry());
    });
  }

  void _removeDateEntry(int index) {
    if (_dateEntries.length > 1) {
      setState(() {
        _dateEntries.removeAt(index);
      });
    }
  }

  void _saveUnavailableDates() {
    final validDates =
        _dateEntries
            .where((entry) => entry.selectedDate != null)
            .map((entry) => entry.toEntity())
            .toList();

    widget.onSave(validDates);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Container(
        width: 800,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Mark Unavailable Date',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: AppConstants.primaryColor),
                      ),
                      child: Text(
                        '${_dateEntries.length} Days',
                        style: TextStyle(
                          color: AppConstants.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: _addNewDateEntry,
                      icon: Icon(Icons.add, color: AppConstants.primaryColor),
                      tooltip: 'Add another date',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Date entries
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    for (int i = 0; i < _dateEntries.length; i++)
                      _buildDateEntryCard(i),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: CustomOutlinedButton(
                    text: 'Cancel',
                    onPressed: () => Navigator.of(context).pop(),
                    foregroundColor: Colors.grey[600]!,
                    borderColor: Colors.grey[300]!,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomElevatedButton(
                    text: 'Save',
                    onPressed: _saveUnavailableDates,
                    backgroundColor: AppConstants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateEntryCard(int index) {
    final entry = _dateEntries[index];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.primaryColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date row
          Row(
            children: [
              const Text(
                'Date',
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
              ),
              const Spacer(),
              const Text(
                'Time',
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
              ),
              const SizedBox(width: 100),
              if (_dateEntries.length > 1)
                IconButton(
                  onPressed: () => _removeDateEntry(index),
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  tooltip: 'Remove date',
                ),
            ],
          ),
          const SizedBox(height: 12),

          // Date picker and time controls
          Row(
            children: [
              // Date picker
              Expanded(
                flex: 2,
                child: InkWell(
                  onTap: () => _selectDate(index),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Text(
                          entry.selectedDate != null
                              ? '${entry.selectedDate!.day} ${_getMonthName(entry.selectedDate!.month)}, ${entry.selectedDate!.year}'
                              : 'Select Date',
                          style: TextStyle(
                            color:
                                entry.selectedDate != null
                                    ? Colors.black87
                                    : Colors.grey[600],
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.calendar_today,
                          color: AppConstants.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Time controls
              if (!entry.isFullDay) ...[
                // Start time
                Expanded(
                  child: _buildTimePickerField(
                    label: 'Start Time',
                    value: entry.startTime,
                    onTap: () => _selectTime(index, true),
                  ),
                ),
                const SizedBox(width: 8),
                const Text('To'),
                const SizedBox(width: 8),
                // End time
                Expanded(
                  child: _buildTimePickerField(
                    label: 'End Time',
                    value: entry.endTime,
                    onTap: () => _selectTime(index, false),
                  ),
                ),
              ] else ...[
                const Expanded(flex: 2, child: SizedBox()),
              ],

              const SizedBox(width: 16),

              // All Day toggle
              Row(
                children: [
                  Switch(
                    value: entry.isFullDay,
                    onChanged: (value) {
                      setState(() {
                        entry.isFullDay = value;
                      });
                    },
                    activeTrackColor: AppConstants.primaryColor,
                    activeThumbColor: Colors.white,
                  ),
                  const Text('All Day'),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(int index) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dateEntries[index].selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _dateEntries[index].selectedDate = picked;
      });
    }
  }

  Widget _buildTimePickerField({
    required String label,
    required String? value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Text(
              value ?? 'Select Time',
              style: TextStyle(
                color: value != null ? Colors.black87 : Colors.grey[600],
              ),
            ),
            const Spacer(),
            Icon(Icons.access_time, color: AppConstants.primaryColor, size: 20),
          ],
        ),
      ),
    );
  }

  Future<void> _selectTime(int index, bool isStartTime) async {
    final entry = _dateEntries[index];

    // Parse current time if available
    TimeOfDay initialTime = TimeOfDay.now();
    if (isStartTime && entry.startTime != null) {
      initialTime = _parseTimeString(entry.startTime!);
    } else if (!isStartTime && entry.endTime != null) {
      initialTime = _parseTimeString(entry.endTime!);
    }

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (picked != null && mounted) {
      final formattedTime = picked.format(context);
      setState(() {
        if (isStartTime) {
          entry.startTime = formattedTime;
        } else {
          entry.endTime = formattedTime;
        }
      });
    }
  }

  TimeOfDay _parseTimeString(String timeString) {
    try {
      // Parse time string in format "h:mm AM/PM"
      final parts = timeString.split(' ');
      final timePart = parts[0];
      final period = parts.length > 1 ? parts[1] : 'AM';

      final timeParts = timePart.split(':');
      int hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      if (period.toUpperCase() == 'PM' && hour != 12) {
        hour += 12;
      } else if (period.toUpperCase() == 'AM' && hour == 12) {
        hour = 0;
      }

      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      return TimeOfDay.now();
    }
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }
}

/// Helper class for managing unavailable date entries
class _UnavailableDateEntry {
  DateTime? selectedDate;
  bool isFullDay;
  String? startTime;
  String? endTime;

  _UnavailableDateEntry({
    this.selectedDate,
    this.isFullDay = true,
    this.startTime,
    this.endTime,
  });

  factory _UnavailableDateEntry.fromEntity(UnavailableDate entity) {
    return _UnavailableDateEntry(
      selectedDate: entity.date,
      isFullDay: entity.isFullDay,
      startTime:
          entity.timeSlot?.startTime != null
              ? AvailabilityTimeSlot.convertTo12HourFormat(
                entity.timeSlot!.startTime,
              )
              : null,
      endTime:
          entity.timeSlot?.endTime != null
              ? AvailabilityTimeSlot.convertTo12HourFormat(
                entity.timeSlot!.endTime,
              )
              : null,
    );
  }

  UnavailableDate toEntity() {
    return UnavailableDate(
      date: selectedDate!,
      isFullDay: isFullDay,
      timeSlot:
          isFullDay || startTime == null || endTime == null
              ? null
              : AvailabilityTimeSlot(
                startTime: AvailabilityTimeSlot.convertTo24HourFormat(
                  startTime!,
                ),
                endTime: AvailabilityTimeSlot.convertTo24HourFormat(endTime!),
              ),
    );
  }
}
